const days = document.getElementById('days');
const hours = document.getElementById('hours');
const minutes = document.getElementById('minutes');
const seconds = document.getElementById('seconds');
const countdown = document.getElementById('countdown');
const year = document.getElementById('year');
const loading = document.getElementById('loading');

const currentYear = new Date().getFullYear();

const newYearTime = new Date(`January 01 ${currentYear + 1} 00:00:00`);

// Set background year
year.innerText = currentYear + 1;

// Update background color to yellow and show Labor Day greeting
document.body.style.backgroundColor = 'yellow';

// Create and show Labor Day greeting
const laborDayMsg = document.createElement('div');
laborDayMsg.innerText = '劳动节快乐';
laborDayMsg.style.position = 'fixed';
laborDayMsg.style.top = '20px';
laborDayMsg.style.left = '50%';
laborDayMsg.style.transform = 'translateX(-50%)';
laborDayMsg.style.background = 'rgba(255,255,0,0.95)';
laborDayMsg.style.color = '#333';
laborDayMsg.style.fontSize = '2rem';
laborDayMsg.style.fontWeight = 'bold';
laborDayMsg.style.padding = '12px 32px';
laborDayMsg.style.borderRadius = '12px';
laborDayMsg.style.boxShadow = '0 2px 8px rgba(0,0,0,0.1)';
laborDayMsg.style.zIndex = '9999';
document.body.appendChild(laborDayMsg);

// Update countdown time
function updateCountdown() {
  const currentTime = new Date();
  const diff = newYearTime - currentTime;

  const d = Math.floor(diff / 1000 / 60 / 60 / 24);
  const h = Math.floor(diff / 1000 / 60 / 60) % 24;
  const m = Math.floor(diff / 1000 / 60) % 60;
  const s = Math.floor(diff / 1000) % 60;

  // Add values to DOM
  days.innerHTML = d;
  hours.innerHTML = h < 10 ? '0' + h : h;
  minutes.innerHTML = m < 10 ? '0' + m : m;
  seconds.innerHTML = s < 10 ? '0' + s : s;
}

// Show spinner before countdown
setTimeout(() => {
  loading.remove();
  countdown.style.display = 'flex';
}, 1000);

// Run every second
setInterval(updateCountdown, 1000);
