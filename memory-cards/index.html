<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta http-equiv="X-UA-Compatible" content="ie=edge" />
    <link
      rel="stylesheet"
      href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.11.2/css/all.min.css"
      integrity="sha256-+N4/V/SbAFiW1MPBCXnfnP9QSN3+Keu+NlB+0ev/YKQ="
      crossorigin="anonymous"
    />
    <link rel="stylesheet" href="style.css" />
    <title>Memory Cards</title>
  </head>
  <body>
    <button id="clear" class="clear btn">
      <i class="fas fa-trash"></i> Clear Cards
    </button>

    <h1>
      Memory Cards
      <button id="show" class="btn btn-small">
        <i class="fas fa-plus"></i> Add New Card
      </button>
    </h1>

    <div id="cards-container" class="cards">
      <!-- <div class="card active">
        <div class="inner-card">
          <div class="inner-card-front">
            <p>
              What is PHP?
            </p>
          </div>
          <div class="inner-card-back">
            <p>
              A programming language
            </p>
          </div>
        </div>
      </div>

      <div class="card">
        <div class="inner-card">
          <div class="inner-card-front">
            <p>
              What is PHP?
            </p>
          </div>
          <div class="inner-card-back">
            <p>
              A programming language
            </p>
          </div>
        </div>
      </div> -->
    </div>

    <div class="navigation">
      <button id="prev" class="nav-button">
        <i class="fas fa-arrow-left"></i>
      </button>

      <p id="current"></p>

      <button id="next" class="nav-button">
        <i class="fas fa-arrow-right"></i>
      </button>
    </div>

    <div id="add-container" class="add-container">
      <h1>
        Add New Card
        <button id="hide" class="btn btn-small btn-ghost">
          <i class="fas fa-times"></i>
        </button>
      </h1>

      <div class="form-group">
        <label for="question">Question</label>
        <textarea id="question" placeholder="Enter question..."></textarea>
      </div>

      <div class="form-group">
        <label for="answer">Answer</label>
        <textarea id="answer" placeholder="Enter Answer..."></textarea>
      </div>

      <button id="add-card" class="btn">
        <i class="fas fa-plus"></i> Add Card
      </button>
    </div>

    <script src="script.js"></script>
  </body>
</html>
