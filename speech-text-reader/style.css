@import url('https://fonts.googleapis.com/css?family=Lato');

* {
  box-sizing: border-box;
}

body {
  background: #ffefea;
  font-family: 'Lato', sans-serif;
  min-height: 100vh;
  margin: 0;
}

h1 {
  text-align: center;
}

.container {
  margin: auto;
  padding: 20px;
}

.btn {
  cursor: pointer;
  background-color: darksalmon;
  border: 0;
  border-radius: 4px;
  color: #fff;
  font-size: 16px;
  padding: 8px;
}

.btn:active {
  transform: scale(0.98);
}

.btn:focus,
select:focus {
  outline: 0;
}

.btn-toggle {
  display: block;
  margin: auto;
  margin-bottom: 20px;
}

.text-box {
  width: 70%;
  position: absolute;
  top: 30%;
  left: 50%;
  transform: translate(-50%, -800px);
  background-color: #333;
  color: #fff;
  padding: 20px;
  border-radius: 5px;
  transition: all 1s ease-in-out;
}

.text-box.show {
  transform: translate(-50%, 0);
}

.text-box select {
  background-color: darksalmon;
  border: 0;
  color: #fff;
  font-size: 12px;
  height: 30px;
  width: 100%;
}

.text-box textarea {
  border: 1px #dadada solid;
  border-radius: 4px;
  font-size: 16px;
  padding: 8px;
  margin: 15px 0;
  width: 100%;
  height: 150px;
}

.text-box .btn {
  width: 100%;
}

.text-box .close {
  float: right;
  text-align: right;
  cursor: pointer;
}

main {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  grid-gap: 10px;
}

.box {
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
  border-radius: 5px;
  cursor: pointer;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  transition: box-shadow 0.2s ease-out;
}

.box.active {
  box-shadow: 0 0 10px 5px darksalmon;
}

.box img {
  width: 100%;
  object-fit: cover;
  height: 200px;
}

.box .info {
  background-color: darksalmon;
  color: #fff;
  font-size: 18px;
  letter-spacing: 1px;
  text-transform: uppercase;
  margin: 0;
  padding: 10px;
  text-align: center;
  height: 100%;
}

@media (max-width: 1100px) {
  main {
    grid-template-columns: repeat(3, 1fr);
  }
}

@media (max-width: 760px) {
  main {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 500px) {
  main {
    grid-template-columns: 1fr;
  }
}
