@import url('https://fonts.googleapis.com/css?family=Lato&display=swap');

:root {
  --border-color: #e3e5e4;
  --background-color: #c3c7ca;
  --text-color: #34444f;
}

* {
  box-sizing: border-box;
}

body {
  background-color: #fff;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
  height: 100vh;
  margin: 0;
  font-family: 'Lato', sans-serif;
}

.draggable-list {
  border: 1px solid var(--border-color);
  color: var(--text-color);
  padding: 0;
  list-style-type: none;
}

.draggable-list li {
  background-color: #fff;
  display: flex;
  flex: 1;
}

.draggable-list li:not(:last-of-type) {
  border-bottom: 1px solid var(--border-color);
}

.draggable-list .number {
  background-color: var(--background-color);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28px;
  height: 60px;
  width: 60px;
}

.draggable-list li.over .draggable {
  background-color: #eaeaea;
}

.draggable-list .person-name {
  margin: 0 20px 0 0;
}

.draggable-list li.right .person-name {
  color: #3ae374;
}

.draggable-list li.wrong .person-name {
  color: #ff3838;
}

.draggable {
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 15px;
  flex: 1;
}

.check-btn {
  background-color: var(--background-color);
  border: none;
  color: var(--text-color);
  font-size: 16px;
  padding: 10px 20px;
  cursor: pointer;
}

.check-btn:active {
  transform: scale(0.98);
}

.check-btn:focus {
  outline: none;
}
