* {
  box-sizing: border-box;
}

body {
  background: #2f3542 url('img/bg.jpg') no-repeat left center/cover;
  color: #fff;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  margin: 0;
  font-family: Arial, Helvetica, sans-serif;
}

h1,
h3 {
  margin-bottom: 0;
}

p {
  line-height: 1.5;
  margin: 0;
}

.play-again {
  padding: 8px 15px;
  border: 0;
  background: #f4f4f4;
  border-radius: 5px;
  margin-top: 10px;
}

.msg {
  font-size: 1.5em;
  margin-top: 40px;
}

.box {
  border: 1px solid #dedede;
  display: inline-block;
  font-size: 30px;
  margin: 20px;
  padding: 10px;
}
