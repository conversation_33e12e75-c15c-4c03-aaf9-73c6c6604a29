* {
  box-sizing: border-box;
}

body {
  background-color: #2c3e50;
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
  margin: 0;
  font-family: Verdana, Geneva, Tahoma, sans-serif;
}

button {
  cursor: pointer;
  font-size: 14px;
  border-radius: 4px;
  padding: 5px 15px;
}

select {
  width: 200px;
  padding: 5px;
  appearance: none;
  -webkit-appearance: none;
  -moz-appearance: none;
  border-radius: 0;
  background-color: #a7c5e3;
}

select:focus,
button:focus {
  outline: 0;
}

.settings-btn {
  position: absolute;
  bottom: 30px;
  left: 30px;
}

.settings {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  background-color: rgba(0, 0, 0, 0.3);
  height: 70px;
  color: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
  transform: translateY(0);
  transition: transform 0.3s ease-in-out;
}

.settings.hide {
  transform: translateY(-100%);
}

.container {
  background-color: #34495e;
  padding: 20px;
  border-radius: 4px;
  box-shadow: 0 3px 5px rgba(0, 0, 0, 0.3);
  color: #fff;
  position: relative;
  text-align: center;
  width: 500px;
}

h2 {
  background-color: rgba(0, 0, 0, 0.3);
  padding: 8px;
  border-radius: 4px;
  margin: 0 0 40px;
}

h1 {
  margin: 0;
}

input {
  border: 0;
  border-radius: 4px;
  font-size: 14px;
  width: 300px;
  padding: 12px 20px;
  margin-top: 10px;
}

.score-container {
  position: absolute;
  top: 60px;
  right: 20px;
}

.time-container {
  position: absolute;
  top: 60px;
  left: 20px;
}

.end-game-container {
  background-color: inherit;
  display: none;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
}
